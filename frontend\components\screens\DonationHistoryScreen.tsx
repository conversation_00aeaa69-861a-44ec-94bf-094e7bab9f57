import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, FlatList } from 'react-native';
import { <PERSON>, But<PERSON>, LoadingSpinner } from '../ui';

interface DonationRecord {
  id: string;
  date: string;
  location: string;
  bloodType: string;
  amount: string;
  recipient?: string;
  status: 'completed' | 'pending' | 'cancelled';
}

function DonationHistoryScreen() {
  const [donations, setDonations] = useState<DonationRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalDonations: 0,
    totalAmount: 0,
    livesSaved: 0,
    lastDonation: '',
  });

  useEffect(() => {
    fetchDonationHistory();
  }, []);

  const fetchDonationHistory = async () => {
    setLoading(true);
    try {
      // TODO: Replace with actual API call to fetch donation history
      await new Promise(resolve => setTimeout(resolve, 1000));

      // No dummy data - start with empty array
      const donations: DonationRecord[] = [];

      setDonations(donations);
      setStats({
        totalDonations: 0,
        totalAmount: 0,
        livesSaved: 0,
        lastDonation: '',
      });
    } catch (error) {
      console.error('Error fetching donation history:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return '#4CAF50';
      case 'pending': return '#FFC107';
      case 'cancelled': return '#F44336';
      default: return '#666666';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return '✅';
      case 'pending': return '⏳';
      case 'cancelled': return '❌';
      default: return '❓';
    }
  };

  const renderDonationItem = ({ item }: { item: DonationRecord }) => (
    <Card style={styles.donationCard}>
      <View style={styles.donationHeader}>
        <Text style={styles.donationDate}>{new Date(item.date).toLocaleDateString()}</Text>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
          <Text style={styles.statusText}>
            {getStatusIcon(item.status)} {item.status.toUpperCase()}
          </Text>
        </View>
      </View>
      
      <Text style={styles.donationLocation}>📍 {item.location}</Text>
      <Text style={styles.donationDetails}>🩸 {item.bloodType} • {item.amount}</Text>
      
      {item.recipient && (
        <Text style={styles.donationRecipient}>👤 For: {item.recipient}</Text>
      )}
    </Card>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <LoadingSpinner
          variant="bloodDrop"
          text="Loading donation history..."
          size="large"
        />
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <Card style={styles.statsCard}>
        <Text style={styles.sectionTitle}>Donation Statistics</Text>
        <View style={styles.statsGrid}>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>{stats.totalDonations}</Text>
            <Text style={styles.statLabel}>Total Donations</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>{stats.totalAmount}ml</Text>
            <Text style={styles.statLabel}>Blood Donated</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>{stats.livesSaved}</Text>
            <Text style={styles.statLabel}>Lives Saved</Text>
          </View>
        </View>
      </Card>

      <View style={styles.historyHeader}>
        <Text style={styles.sectionTitle}>Donation History</Text>
        <Button
          title="📊 Export Report"
          variant="secondary"
          size="small"
          onPress={() => {
            // TODO: Implement export functionality
            console.log('Export donation report');
          }}
        />
      </View>

      <FlatList
        data={donations}
        keyExtractor={(item) => item.id}
        renderItem={renderDonationItem}
        scrollEnabled={false}
        ListEmptyComponent={
          <Card style={styles.emptyCard}>
            <Text style={styles.emptyText}>
              🩸 No donation history found.{'\n'}
              Start your journey as a life-saver today!
            </Text>
          </Card>
        }
      />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
  },
  statsCard: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#D32F2F',
  },
  statLabel: {
    fontSize: 12,
    color: '#666666',
    textAlign: 'center',
    marginTop: 4,
  },
  historyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  donationCard: {
    marginBottom: 12,
  },
  donationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  donationDate: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 10,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  donationLocation: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 4,
  },
  donationDetails: {
    fontSize: 14,
    color: '#333333',
    marginBottom: 4,
  },
  donationRecipient: {
    fontSize: 12,
    color: '#666666',
    fontStyle: 'italic',
  },
  emptyCard: {
    alignItems: 'center',
    padding: 32,
  },
  emptyText: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 24,
  },
});

DonationHistoryScreen.displayName = 'DonationHistoryScreen';

export default DonationHistoryScreen;
